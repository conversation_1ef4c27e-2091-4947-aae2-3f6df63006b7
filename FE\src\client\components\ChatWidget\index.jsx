import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../../../shared/contexts/AuthContext';
import { useSocket } from '../../../shared/contexts/SocketContext';
import { chatAPI } from '../../../shared/services/api';
import styles from './ChatWidget.module.css';

const ChatWidget = () => {
  const { user, isAuthenticated } = useAuth();
  const { isConnected, joinRoom, leaveRoom, sendMessage } = useSocket();
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);
  const roomId = user ? `user_${user.id}` : null;

  // Scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Load messages when chat opens
  useEffect(() => {
    if (isOpen && isAuthenticated && roomId) {
      loadMessages();
      joinRoom(roomId);
      
      return () => {
        leaveRoom(roomId);
      };
    }
  }, [isOpen, isAuthenticated, roomId]);

  // Listen for new messages
  useEffect(() => {
    if (isConnected) {
      const socket = useSocket().socket;
      
      socket?.on('new_message', (message) => {
        if (message.room_id === roomId) {
          setMessages(prev => [...prev, message]);
        }
      });

      socket?.on('user_typing', ({ roomId: typingRoomId, isTyping: typing }) => {
        if (typingRoomId === roomId) {
          setIsTyping(typing);
        }
      });

      return () => {
        socket?.off('new_message');
        socket?.off('user_typing');
      };
    }
  }, [isConnected, roomId]);

  const loadMessages = async () => {
    try {
      setLoading(true);
      const response = await chatAPI.getMessages(roomId);
      if (response.success) {
        setMessages(response.data);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (!newMessage.trim() || !isAuthenticated) return;

    const messageData = {
      content: newMessage.trim(),
      room_id: roomId
    };

    try {
      const response = await chatAPI.sendMessage(messageData);
      if (response.success) {
        setMessages(prev => [...prev, response.data]);
        setNewMessage('');
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!isAuthenticated) {
    return null; // Don't show chat widget for unauthenticated users
  }

  return (
    <div className={styles.chatWidget}>
      {/* Chat Toggle Button */}
      <button
        className={`${styles.chatToggle} ${isOpen ? styles.open : ''}`}
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? '✕' : '💬'}
        <span className={styles.chatLabel}>
          {isOpen ? 'Đóng chat' : 'Chat với admin'}
        </span>
      </button>

      {/* Chat Window */}
      {isOpen && (
        <div className={styles.chatWindow}>
          {/* Chat Header */}
          <div className={styles.chatHeader}>
            <div className={styles.headerInfo}>
              <div className={styles.adminAvatar}>👨‍💼</div>
              <div>
                <h4>Hỗ trợ khách hàng</h4>
                <span className={`${styles.status} ${isConnected ? styles.online : styles.offline}`}>
                  {isConnected ? 'Đang online' : 'Offline'}
                </span>
              </div>
            </div>
          </div>

          {/* Messages Area */}
          <div className={styles.messagesArea}>
            {loading ? (
              <div className={styles.loading}>
                <div className={styles.spinner}></div>
                <span>Đang tải tin nhắn...</span>
              </div>
            ) : messages.length === 0 ? (
              <div className={styles.emptyState}>
                <p>Chưa có tin nhắn nào. Hãy bắt đầu cuộc trò chuyện!</p>
              </div>
            ) : (
              messages.map((message, index) => (
                <div
                  key={message._id || index}
                  className={`${styles.message} ${
                    message.sender_role === 'admin' ? styles.admin : styles.user
                  }`}
                >
                  <div className={styles.messageContent}>
                    <p>{message.content}</p>
                    <span className={styles.messageTime}>
                      {formatTime(message.createdAt)}
                    </span>
                  </div>
                </div>
              ))
            )}
            
            {isTyping && (
              <div className={`${styles.message} ${styles.admin}`}>
                <div className={styles.typingIndicator}>
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* Message Input */}
          <form className={styles.messageForm} onSubmit={handleSendMessage}>
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Nhập tin nhắn..."
              className={styles.messageInput}
              disabled={!isConnected}
            />
            <button
              type="submit"
              className={styles.sendButton}
              disabled={!newMessage.trim() || !isConnected}
            >
              ➤
            </button>
          </form>
        </div>
      )}
    </div>
  );
};

export default ChatWidget;
