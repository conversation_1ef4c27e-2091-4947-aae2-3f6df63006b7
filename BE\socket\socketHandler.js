const jwt = require('jsonwebtoken');
const Account = require('../model/account.model');
const Chat = require('../model/Chat.model');
require('dotenv').config();

// Store connected users
const connectedUsers = new Map();

const socketHandler = (io) => {
  // Middleware for socket authentication
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication error'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await Account.findById(decoded.id).select('-password');
      
      if (!user || user.deleted) {
        return next(new Error('Authentication error'));
      }

      socket.user = user;
      next();
    } catch (error) {
      next(new Error('Authentication error'));
    }
  });

  io.on('connection', (socket) => {
    console.log(`User connected: ${socket.user.fullName} (${socket.id})`);
    
    // Store connected user
    connectedUsers.set(socket.user._id.toString(), {
      socketId: socket.id,
      user: socket.user,
      lastSeen: new Date()
    });

    // Emit online users to all clients
    io.emit('online_users', Array.from(connectedUsers.values()).map(u => ({
      id: u.user._id,
      fullName: u.user.fullName,
      role_id: u.user.role_id
    })));

    // Join user to their personal room
    const userRoom = `user_${socket.user._id}`;
    socket.join(userRoom);

    // Handle joining specific rooms
    socket.on('join_room', (roomId) => {
      socket.join(roomId);
      console.log(`User ${socket.user.fullName} joined room: ${roomId}`);
    });

    // Handle leaving rooms
    socket.on('leave_room', (roomId) => {
      socket.leave(roomId);
      console.log(`User ${socket.user.fullName} left room: ${roomId}`);
    });

    // Handle sending messages
    socket.on('send_message', async (messageData) => {
      try {
        const { content, room_id } = messageData;
        
        if (!content || !room_id) {
          socket.emit('error', { message: 'Missing required fields' });
          return;
        }

        const newChat = new Chat({
          account_id: socket.user._id,
          content,
          room_id,
          sender_role: socket.user.role_id === 'admin' ? 'admin' : 'user'
        });

        await newChat.save();
        await newChat.populate('account_id', 'fullName email role_id');

        // Emit to all users in the room
        io.to(room_id).emit('new_message', newChat);
        
        console.log(`Message sent in room ${room_id} by ${socket.user.fullName}`);
      } catch (error) {
        console.error('Error sending message:', error);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });

    // Handle typing indicators
    socket.on('typing', ({ roomId, isTyping }) => {
      socket.to(roomId).emit('user_typing', {
        userId: socket.user._id,
        userName: socket.user.fullName,
        roomId,
        isTyping
      });
    });

    // Handle disconnect
    socket.on('disconnect', () => {
      console.log(`User disconnected: ${socket.user.fullName} (${socket.id})`);
      
      // Remove from connected users
      connectedUsers.delete(socket.user._id.toString());
      
      // Emit updated online users
      io.emit('online_users', Array.from(connectedUsers.values()).map(u => ({
        id: u.user._id,
        fullName: u.user.fullName,
        role_id: u.user.role_id
      })));
    });

    // Handle errors
    socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  });
};

module.exports = socketHandler;
