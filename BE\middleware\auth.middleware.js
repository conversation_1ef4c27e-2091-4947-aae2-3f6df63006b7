const jwt = require('jsonwebtoken');
const Account = require('../model/account.model');
require('dotenv').config();

const authMiddleware = async (req, res, next) => {
    try {
        const token = req.header('Authorization')?.replace('Bearer ', '');

        if (!token) {
            return res.status(401).json({
                success: false,
                message: 'Không có token, truy cập bị từ chối'
            });
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await Account.findById(decoded.id).select('-password');

        if (!user || user.deleted) {
            return res.status(401).json({
                success: false,
                message: 'Token không hợp lệ'
            });
        }

        req.user = user;
        next();
    } catch (error) {
        console.error('Auth middleware error:', error);
        res.status(401).json({
            success: false,
            message: 'Token không hợp lệ'
        });
    }
};

module.exports = authMiddleware;