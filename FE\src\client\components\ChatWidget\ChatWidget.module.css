/* Chat Widget Container */
.chatWidget {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Chat Toggle Button */
.chatToggle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.chatToggle:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
}

.chatToggle.open {
  background: #e74c3c;
}

.chatLabel {
  position: absolute;
  right: 70px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 14px;
  white-space: nowrap;
  opacity: 0;
  transform: translateX(10px);
  transition: all 0.3s ease;
  pointer-events: none;
}

.chatToggle:hover .chatLabel {
  opacity: 1;
  transform: translateX(0);
}

/* Chat Window */
.chatWindow {
  position: absolute;
  bottom: 80px;
  right: 0;
  width: 350px;
  height: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Chat Header */
.chatHeader {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.headerInfo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.adminAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.headerInfo h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.status {
  font-size: 12px;
  opacity: 0.9;
}

.status.online {
  color: #2ecc71;
}

.status.offline {
  color: #e74c3c;
}

/* Messages Area */
.messagesArea {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  background: #f8f9fa;
}

.messagesArea::-webkit-scrollbar {
  width: 4px;
}

.messagesArea::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.messagesArea::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

/* Loading State */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #666;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

/* Messages */
.message {
  display: flex;
  margin-bottom: 8px;
}

.message.user {
  justify-content: flex-end;
}

.message.admin {
  justify-content: flex-start;
}

.messageContent {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 18px;
  position: relative;
}

.message.user .messageContent {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom-right-radius: 4px;
}

.message.admin .messageContent {
  background: white;
  color: #333;
  border: 1px solid #e1e8ed;
  border-bottom-left-radius: 4px;
}

.messageContent p {
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
}

.messageTime {
  font-size: 11px;
  opacity: 0.7;
  display: block;
  margin-top: 4px;
}

/* Typing Indicator */
.typingIndicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 12px 16px;
}

.typingIndicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #999;
  animation: typing 1.4s infinite ease-in-out;
}

.typingIndicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typingIndicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Message Form */
.messageForm {
  display: flex;
  padding: 16px;
  background: white;
  border-top: 1px solid #e1e8ed;
  gap: 8px;
}

.messageInput {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #e1e8ed;
  border-radius: 24px;
  outline: none;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.messageInput:focus {
  border-color: #667eea;
}

.messageInput:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.sendButton {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sendButton:hover:not(:disabled) {
  transform: scale(1.05);
}

.sendButton:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

/* Responsive */
@media (max-width: 768px) {
  .chatWindow {
    width: 300px;
    height: 450px;
  }
  
  .chatWidget {
    bottom: 15px;
    right: 15px;
  }
}
