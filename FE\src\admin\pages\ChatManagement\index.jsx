import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../../../shared/contexts/AuthContext';
import { useSocket } from '../../../shared/contexts/SocketContext';
import { chatAPI } from '../../../shared/services/api';

const ChatManagement = () => {
  const { user } = useAuth();
  const { isConnected, joinRoom, leaveRoom } = useSocket();
  const [chatRooms, setChatRooms] = useState([]);
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [roomsLoading, setRoomsLoading] = useState(true);
  const messagesEndRef = useRef(null);

  // Scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Load chat rooms on component mount
  useEffect(() => {
    loadChatRooms();
  }, []);

  // Listen for new messages
  useEffect(() => {
    if (isConnected && selectedRoom) {
      const socket = useSocket().socket;
      
      socket?.on('new_message', (message) => {
        if (message.room_id === selectedRoom._id) {
          setMessages(prev => [...prev, message]);
        }
        // Update room list to show new message
        loadChatRooms();
      });

      return () => {
        socket?.off('new_message');
      };
    }
  }, [isConnected, selectedRoom]);

  const loadChatRooms = async () => {
    try {
      setRoomsLoading(true);
      const response = await chatAPI.getChatRooms();
      if (response.success) {
        setChatRooms(response.data);
      }
    } catch (error) {
      console.error('Error loading chat rooms:', error);
    } finally {
      setRoomsLoading(false);
    }
  };

  const loadMessages = async (roomId) => {
    try {
      setLoading(true);
      const response = await chatAPI.getMessages(roomId);
      if (response.success) {
        setMessages(response.data);
        // Mark messages as read
        await chatAPI.markAsRead(roomId);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRoomSelect = (room) => {
    if (selectedRoom) {
      leaveRoom(selectedRoom._id);
    }
    
    setSelectedRoom(room);
    joinRoom(room._id);
    loadMessages(room._id);
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (!newMessage.trim() || !selectedRoom) return;

    const messageData = {
      content: newMessage.trim(),
      room_id: selectedRoom._id
    };

    try {
      const response = await chatAPI.sendMessage(messageData);
      if (response.success) {
        setMessages(prev => [...prev, response.data]);
        setNewMessage('');
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDate = (timestamp) => {
    return new Date(timestamp).toLocaleDateString('vi-VN');
  };

  return (
    <div className="h-full bg-gray-50">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Quản lý Chat</h1>
        <p className="text-gray-600">Trả lời tin nhắn từ người dùng</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border h-[calc(100vh-200px)] flex">
        {/* Chat Rooms List */}
        <div className="w-1/3 border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <h2 className="font-semibold text-gray-900">Cuộc trò chuyện</h2>
            <div className="flex items-center mt-2">
              <div className={`w-2 h-2 rounded-full mr-2 ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-sm text-gray-600">
                {isConnected ? 'Đang online' : 'Offline'}
              </span>
            </div>
          </div>
          
          <div className="flex-1 overflow-y-auto">
            {roomsLoading ? (
              <div className="p-4 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-sm text-gray-600 mt-2">Đang tải...</p>
              </div>
            ) : chatRooms.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                <p>Chưa có cuộc trò chuyện nào</p>
              </div>
            ) : (
              chatRooms.map((room) => (
                <div
                  key={room._id}
                  className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                    selectedRoom?._id === room._id ? 'bg-blue-50 border-blue-200' : ''
                  }`}
                  onClick={() => handleRoomSelect(room)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-blue-600">
                            {room.participants?.[0]?.fullName?.charAt(0) || 'U'}
                          </span>
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900 text-sm">
                            {room.participants?.[0]?.fullName || 'Người dùng'}
                          </h3>
                          <p className="text-xs text-gray-500">
                            {room.participants?.[0]?.email}
                          </p>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mt-2 truncate">
                        {room.lastMessage}
                      </p>
                    </div>
                    <div className="text-xs text-gray-400">
                      {formatTime(room.lastMessageTime)}
                    </div>
                  </div>
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-xs text-gray-500">
                      {room.messageCount} tin nhắn
                    </span>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Chat Messages */}
        <div className="flex-1 flex flex-col">
          {selectedRoom ? (
            <>
              {/* Chat Header */}
              <div className="p-4 border-b border-gray-200 bg-white">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="font-medium text-blue-600">
                      {selectedRoom.participants?.[0]?.fullName?.charAt(0) || 'U'}
                    </span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      {selectedRoom.participants?.[0]?.fullName || 'Người dùng'}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {selectedRoom.participants?.[0]?.email}
                    </p>
                  </div>
                </div>
              </div>

              {/* Messages Area */}
              <div className="flex-1 overflow-y-auto p-4 bg-gray-50">
                {loading ? (
                  <div className="flex justify-center items-center h-full">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  </div>
                ) : messages.length === 0 ? (
                  <div className="flex justify-center items-center h-full text-gray-500">
                    <p>Chưa có tin nhắn nào</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {messages.map((message, index) => (
                      <div
                        key={message._id || index}
                        className={`flex ${
                          message.sender_role === 'admin' ? 'justify-end' : 'justify-start'
                        }`}
                      >
                        <div
                          className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                            message.sender_role === 'admin'
                              ? 'bg-blue-600 text-white'
                              : 'bg-white text-gray-900 border border-gray-200'
                          }`}
                        >
                          <p className="text-sm">{message.content}</p>
                          <p className={`text-xs mt-1 ${
                            message.sender_role === 'admin' ? 'text-blue-100' : 'text-gray-500'
                          }`}>
                            {formatTime(message.createdAt)}
                          </p>
                        </div>
                      </div>
                    ))}
                    <div ref={messagesEndRef} />
                  </div>
                )}
              </div>

              {/* Message Input */}
              <form onSubmit={handleSendMessage} className="p-4 bg-white border-t border-gray-200">
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Nhập tin nhắn..."
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={!isConnected}
                  />
                  <button
                    type="submit"
                    disabled={!newMessage.trim() || !isConnected}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                  >
                    Gửi
                  </button>
                </div>
              </form>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center text-gray-500">
              <div className="text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">💬</span>
                </div>
                <p>Chọn một cuộc trò chuyện để bắt đầu</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatManagement;
