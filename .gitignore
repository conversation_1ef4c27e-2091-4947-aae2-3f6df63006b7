# Node modules
node_modules/

# Build output
dist/
build/

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Environment variables
.env
.env.local
.env.*.local

# VS Code settings
.vscode/

# OS-specific
.DS_Store
Thumbs.db

# Optional: Ignore coverage reports
coverage/

# Optional: Ignore local database
*.sqlite
*.db

# Optional: Ignore IDEs
.idea/
*.iml
