/* Documents page specific styles */
.documentsPage {
  background-color: #f9fafb;
  min-height: 100vh;
}

.pageHeader {
  background: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.headerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.pageTitle {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.pageSubtitle {
  color: #4b5563;
}

.mainContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
  display: grid;
  grid-template-columns: 3fr 1fr;
  gap: 2rem;
}

.contentArea {
  /* Main content styles */
}

.sidebar {
  /* Sidebar styles */
}

.searchSection {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.searchRow {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.searchInput {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  outline: none;
  transition: all 0.3s ease;
}

.searchInput:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.searchButton {
  background: #2563eb;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.searchButton:hover {
  background: #1d4ed8;
}

.filterButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filterBtn {
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.filterBtn.active {
  background: #2563eb;
  color: white;
}

.filterBtn:not(.active) {
  background: #f3f4f6;
  color: #4b5563;
}

.filterBtn:not(.active):hover {
  background: #eff6ff;
  color: #2563eb;
}

.documentsContainer {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.documentCard {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.documentCard:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.documentHeader {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.documentContent {
  flex: 1;
}

.documentActions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 120px;
}

.documentTags {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.documentType {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
}

.typeLaw {
  background: #fee2e2;
  color: #dc2626;
}

.typeDecree {
  background: #dbeafe;
  color: #2563eb;
}

.typeCircular {
  background: #dcfce7;
  color: #059669;
}

.typeDecision {
  background: #fef3c7;
  color: #d97706;
}

.typeDirective {
  background: #f3e8ff;
  color: #9333ea;
}

.statusTag {
  background: #dcfce7;
  color: #059669;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
}

.documentTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: color 0.3s ease;
}

.documentTitle:hover {
  color: #2563eb;
}

.documentSummary {
  color: #4b5563;
  font-size: 0.875rem;
  margin-bottom: 1rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.documentDetails {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  font-size: 0.875rem;
}

.detailItem {
  display: flex;
  flex-direction: column;
}

.detailLabel {
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.detailValue {
  font-weight: 600;
  color: #1f2937;
}

.actionButton {
  background: #2563eb;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.actionButton:hover {
  background: #1d4ed8;
}

.secondaryButton {
  border: 1px solid #d1d5db;
  color: #4b5563;
  background: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.secondaryButton:hover {
  background: #f9fafb;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
}

.paginationButtons {
  display: flex;
  gap: 0.5rem;
}

.paginationBtn {
  padding: 0.5rem 1rem;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.paginationBtn:hover {
  background: #f9fafb;
}

.paginationBtn.active {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.sidebarCard {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.sidebarTitle {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1f2937;
}

.quickLinks {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.quickLink {
  color: #2563eb;
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.quickLink:hover {
  color: #1d4ed8;
}

.statsContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.statItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statLabel {
  color: #4b5563;
  font-size: 0.875rem;
}

.statValue {
  font-weight: 700;
}

.statTotal {
  color: #2563eb;
}

.statActive {
  color: #059669;
}

.statInactive {
  color: #dc2626;
}

.statRecent {
  color: #2563eb;
}

/* Responsive design */
@media (max-width: 1024px) {
  .mainContent {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .headerContent {
    padding: 1.5rem 1rem;
  }
  
  .mainContent {
    padding: 1.5rem 1rem;
  }
  
  .searchRow {
    flex-direction: column;
  }
  
  .documentHeader {
    flex-direction: column;
  }
  
  .documentActions {
    flex-direction: row;
    min-width: auto;
  }
  
  .documentDetails {
    grid-template-columns: 1fr;
  }
  
  .filterButtons {
    justify-content: center;
  }
}
