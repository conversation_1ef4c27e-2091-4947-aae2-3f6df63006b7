import React, { createContext, useContext, useEffect, useState } from 'react';
import socketService from '../services/socketService';
import { useAuth } from './AuthContext';

const SocketContext = createContext();

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export const SocketProvider = ({ children }) => {
  const { token, isAuthenticated } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState([]);
  const [onlineUsers, setOnlineUsers] = useState([]);

  useEffect(() => {
    if (isAuthenticated && token) {
      // Connect socket when user is authenticated
      const socket = socketService.connect(token);
      
      socket.on('connect', () => {
        setIsConnected(true);
      });

      socket.on('disconnect', () => {
        setIsConnected(false);
      });

      socket.on('new_message', (message) => {
        setMessages(prev => [...prev, message]);
      });

      socket.on('online_users', (users) => {
        setOnlineUsers(users);
      });

      return () => {
        socketService.disconnect();
        setIsConnected(false);
      };
    } else {
      // Disconnect when user logs out
      socketService.disconnect();
      setIsConnected(false);
      setMessages([]);
      setOnlineUsers([]);
    }
  }, [isAuthenticated, token]);

  const joinRoom = (roomId) => {
    socketService.joinRoom(roomId);
  };

  const leaveRoom = (roomId) => {
    socketService.leaveRoom(roomId);
  };

  const sendMessage = (messageData) => {
    socketService.sendMessage(messageData);
  };

  const sendTyping = (roomId, isTyping) => {
    socketService.sendTyping(roomId, isTyping);
  };

  const value = {
    socket: socketService.getSocket(),
    isConnected,
    messages,
    onlineUsers,
    joinRoom,
    leaveRoom,
    sendMessage,
    sendTyping,
    setMessages
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};
