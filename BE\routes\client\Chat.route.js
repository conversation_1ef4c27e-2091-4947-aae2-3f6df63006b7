const express = require('express');
const router = express.Router();

const controller = require("../../controllers/client/Chat.controller");
const authMiddleware = require("../../middleware/auth.middleware");

// L<PERSON><PERSON> danh sách tin nhắn
router.get("/messages", authMiddleware, controller.getMessages);

// Gửi tin nhắn
router.post("/send", authMiddleware, controller.sendMessage);

// Lấy danh sách phòng chat (admin only)
router.get("/rooms", authMiddleware, controller.getChatRooms);

// Đ<PERSON>h dấu tin nhắn đã đọc
router.put("/mark-read", authMiddleware, controller.markAsRead);

module.exports = router;
