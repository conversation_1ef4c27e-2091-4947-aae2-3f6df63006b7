.detailPage {
  background-color: #f9fafb;
  min-height: 100vh;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  gap: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  text-align: center;
  padding: 3rem 1rem;
  max-width: 600px;
  margin: 0 auto;
}

.error h2 {
  color: #dc2626;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.error p {
  color: #6b7280;
  margin-bottom: 2rem;
}

.breadcrumb {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: grid;
  grid-template-columns: 3fr 1fr;
  gap: 2rem;
}

.breadcrumb .container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.breadcrumbLink {
  color: #3b82f6;
  text-decoration: none;
  font-size: 0.875rem;
}

.breadcrumbLink:hover {
  text-decoration: underline;
}

.breadcrumbSeparator {
  color: #6b7280;
}

.breadcrumbCurrent {
  color: #6b7280;
  font-size: 0.875rem;
}

.mainContent {
  padding: 2rem 0;
}

.article {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 2rem;
}

.articleHeader {
  padding: 2rem 2rem 1rem;
}

.categoryTag {
  display: inline-block;
  background: #dbeafe;
  color: #1d4ed8;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 1rem;
}

.articleTitle {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.25;
  margin-bottom: 1.5rem;
}

.articleMeta {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.metaItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.metaLabel {
  color: #6b7280;
  font-weight: 500;
}

.metaValue {
  color: #374151;
  font-weight: 600;
}

.articleImage {
  margin: 0 2rem 2rem;
}

.articleImage img {
  width: 100%;
  height: auto;
  border-radius: 8px;
  object-fit: cover;
}

.articleContent {
  padding: 0 2rem 2rem;
  color: #374151;
  line-height: 1.75;
  font-size: 1rem;
}

.articleContent h3 {
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 2rem 0 1rem;
}

.articleContent p {
  margin-bottom: 1rem;
}

.articleContent ul {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.articleContent li {
  margin-bottom: 0.5rem;
}

.articleTags {
  padding: 1rem 2rem 2rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.75rem;
}

.tagsLabel {
  font-weight: 600;
  color: #6b7280;
  font-size: 0.875rem;
}

.tag {
  background: #f3f4f6;
  color: #374151;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.articleNavigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.backButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: #3b82f6;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.backButton:hover {
  background: #1d4ed8;
  transform: translateY(-1px);
}

.sidebar {
  padding: 2rem 0;
}

.sidebarCard {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.sidebarTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.relatedNews {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.relatedItem {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

.relatedItem:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.relatedLink {
  display: flex;
  gap: 0.75rem;
  text-decoration: none;
  color: inherit;
}

.relatedLink:hover .relatedTitle {
  color: #3b82f6;
}

.relatedImage {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 6px;
  flex-shrink: 0;
}

.relatedContent {
  flex: 1;
}

.relatedTitle {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
  line-height: 1.4;
  margin-bottom: 0.25rem;
  transition: color 0.2s ease;
}

.relatedDate {
  font-size: 0.75rem;
  color: #6b7280;
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .articleTitle {
    font-size: 1.5rem;
  }

  .articleMeta {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .articleHeader,
  .articleImage,
  .articleContent,
  .articleTags {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .articleImage {
    margin-left: 1rem;
    margin-right: 1rem;
  }
}