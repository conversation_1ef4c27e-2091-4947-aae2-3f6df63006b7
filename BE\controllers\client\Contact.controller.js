const News = require("../../model/News.model")
const Chat = require("../../model/Chat.model")
const Account = require("../../model/account.model")

module.exports.index = async (req, res) => {
    try {
        const chats = await Chat.find({
            deleted: false,
        }).populate('account_id', 'fullName email').sort({ createdAt: -1 });

        res.status(200).json({
            success: true,
            data: chats
        });
    } catch (error) {
        console.error('Error fetching chats:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tải tin nhắn',
            error: error.message
        });
    }
}