const Chat = require("../../model/Chat.model");
const Account = require("../../model/account.model");

// [GET] /chat/messages - <PERSON><PERSON><PERSON>nh s<PERSON>ch tin nhắn
module.exports.getMessages = async (req, res) => {
    try {
        const { room_id } = req.query;
        const filter = { deleted: false };
        
        if (room_id) {
            filter.room_id = room_id;
        }

        const chats = await Chat.find(filter)
            .populate('account_id', 'fullName email role_id')
            .sort({ createdAt: 1 });

        res.status(200).json({
            success: true,
            data: chats
        });
    } catch (error) {
        console.error('Error fetching messages:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tải tin nhắn',
            error: error.message
        });
    }
};

// [POST] /chat/send - Gửi tin nhắn
module.exports.sendMessage = async (req, res) => {
    try {
        const { content, room_id } = req.body;
        const account_id = req.user?.id; // Từ middleware auth

        if (!content || !account_id) {
            return res.status(400).json({
                success: false,
                message: 'Thiếu thông tin cần thiết'
            });
        }

        const newChat = new Chat({
            account_id,
            content,
            room_id: room_id || `user_${account_id}`,
            sender_role: req.user?.role_id === 'admin' ? 'admin' : 'user'
        });

        await newChat.save();
        
        // Populate thông tin user
        await newChat.populate('account_id', 'fullName email role_id');

        // Emit tin nhắn qua socket
        if (global._io) {
            global._io.to(newChat.room_id).emit('new_message', newChat);
        }

        res.status(201).json({
            success: true,
            data: newChat,
            message: 'Gửi tin nhắn thành công'
        });
    } catch (error) {
        console.error('Error sending message:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi gửi tin nhắn',
            error: error.message
        });
    }
};

// [GET] /chat/rooms - Lấy danh sách phòng chat (cho admin)
module.exports.getChatRooms = async (req, res) => {
    try {
        // Chỉ admin mới có thể xem tất cả phòng chat
        if (req.user?.role_id !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Không có quyền truy cập'
            });
        }

        const rooms = await Chat.aggregate([
            { $match: { deleted: false } },
            {
                $group: {
                    _id: '$room_id',
                    lastMessage: { $last: '$content' },
                    lastMessageTime: { $last: '$createdAt' },
                    messageCount: { $sum: 1 },
                    participants: { $addToSet: '$account_id' }
                }
            },
            { $sort: { lastMessageTime: -1 } }
        ]);

        // Populate thông tin participants
        for (let room of rooms) {
            const participants = await Account.find(
                { _id: { $in: room.participants } },
                'fullName email role_id'
            );
            room.participants = participants;
        }

        res.status(200).json({
            success: true,
            data: rooms
        });
    } catch (error) {
        console.error('Error fetching chat rooms:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tải danh sách phòng chat',
            error: error.message
        });
    }
};

// [PUT] /chat/mark-read - Đánh dấu tin nhắn đã đọc
module.exports.markAsRead = async (req, res) => {
    try {
        const { room_id } = req.body;
        const account_id = req.user?.id;

        if (!room_id || !account_id) {
            return res.status(400).json({
                success: false,
                message: 'Thiếu thông tin cần thiết'
            });
        }

        await Chat.updateMany(
            { 
                room_id, 
                account_id: { $ne: account_id },
                read_status: false 
            },
            { read_status: true }
        );

        res.status(200).json({
            success: true,
            message: 'Đã đánh dấu tin nhắn đã đọc'
        });
    } catch (error) {
        console.error('Error marking messages as read:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi đánh dấu tin nhắn',
            error: error.message
        });
    }
};
