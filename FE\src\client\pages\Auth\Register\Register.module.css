.registerPage {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.registerContainer {
  width: 100%;
  max-width: 450px;
}

.registerCard {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  padding: 40px;
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.registerHeader {
  text-align: center;
  margin-bottom: 32px;
}

.registerTitle {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.registerSubtitle {
  color: #6b7280;
  font-size: 16px;
}

.registerForm {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.errorAlert {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px;
  border-radius: 8px;
  font-size: 14px;
  text-align: center;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.formLabel {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.formInput {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.2s ease;
  background-color: white;
}

.formInput:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.formInput:disabled {
  background-color: #f9fafb;
  cursor: not-allowed;
}

.inputError {
  border-color: #dc2626 !important;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
}

.passwordInput {
  position: relative;
}

.passwordToggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px;
  color: #6b7280;
  padding: 4px;
}

.passwordToggle:hover {
  color: #374151;
}

.passwordToggle:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.errorText {
  color: #dc2626;
  font-size: 12px;
  margin-top: 4px;
}

.registerButton {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 14px 20px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.registerButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.registerButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.registerFooter {
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.registerFooter p {
  color: #6b7280;
  font-size: 14px;
}

.loginLink {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 600;
}

.loginLink:hover {
  text-decoration: underline;
}

/* Responsive */
@media (max-width: 480px) {
  .registerCard {
    padding: 24px;
  }
  
  .registerTitle {
    font-size: 24px;
  }
  
  .registerForm {
    gap: 16px;
  }
}
