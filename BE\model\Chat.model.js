const mongoose = require("mongoose");

const chatSchema = new mongoose.Schema({
    account_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Acount', // Tên model Account (lưu ý typo trong model Account)
        required: true
    },
    content: {
        type: String,
        required: true,
        trim: true
    },
    room_id: {
        type: String,
        required: true,
        default: function() {
            return `user_${this.account_id}`;
        }
    },
    sender_role: {
        type: String,
        enum: ['user', 'admin'],
        default: 'user'
    },
    message_type: {
        type: String,
        enum: ['text', 'image', 'file'],
        default: 'text'
    },
    read_status: {
        type: Boolean,
        default: false
    },
    deleted: {
        type: Boolean,
        default: false
    },
    deletedAt: Date
}, {
    timestamps: true,
});

// Index để tối ưu query
chatSchema.index({ room_id: 1, createdAt: 1 });
chatSchema.index({ account_id: 1 });
chatSchema.index({ deleted: 1 });

const Chat = mongoose.model("Chat", chatSchema, "chats");

module.exports = Chat;